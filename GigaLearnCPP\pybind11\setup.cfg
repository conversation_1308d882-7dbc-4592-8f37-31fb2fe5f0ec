[metadata]
long_description = file: README.rst
long_description_content_type = text/x-rst
description = Seamless operability between C++11 and Python
author = <PERSON><PERSON>
author_email = <EMAIL>
url = https://github.com/pybind/pybind11
license = BSD

classifiers =
    Development Status :: 5 - Production/Stable
    Intended Audience :: Developers
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Utilities
    Programming Language :: C++
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.6
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    License :: OSI Approved :: BSD License
    Programming Language :: Python :: Implementation :: PyPy
    Programming Language :: Python :: Implementation :: CPython
    Programming Language :: C++
    Topic :: Software Development :: Libraries :: Python Modules

keywords =
    C++11
    Python bindings

project_urls =
    Documentation = https://pybind11.readthedocs.io/
    Bug Tracker = https://github.com/pybind/pybind11/issues
    Discussions = https://github.com/pybind/pybind11/discussions
    Changelog = https://pybind11.readthedocs.io/en/latest/changelog.html
    Chat = https://gitter.im/pybind/Lobby

[options]
python_requires = >=3.6
zip_safe = False
