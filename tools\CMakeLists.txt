cmake_minimum_required(VERSION 3.8)

project("GigaLearnDirectMLTest")

# Set C++ version to 20
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find PyTorch
find_package(Torch REQUIRED)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${TORCH_CXX_FLAGS}")

# Create test executable
add_executable(test_directml test_directml.cpp)

# Link PyTorch
target_link_libraries(test_directml "${TORCH_LIBRARIES}")

# Include GigaLearnCPP headers if building as part of main project
if(TARGET GigaLearnCPP)
    target_include_directories(test_directml PRIVATE 
        "${CMAKE_SOURCE_DIR}/GigaLearnCPP/src/public"
        "${CMAKE_SOURCE_DIR}/GigaLearnCPP/src/private"
    )
    target_link_libraries(test_directml GigaLearnCPP)
    
    # Copy compile definitions from main project
    get_target_property(MAIN_COMPILE_DEFS GigaLearnCPP COMPILE_DEFINITIONS)
    if(MAIN_COMPILE_DEFS)
        target_compile_definitions(test_directml PRIVATE ${MAIN_COMPILE_DEFS})
    endif()
else()
    # Standalone build - try to find ONNX Runtime
    find_path(ONNXRUNTIME_INCLUDE_DIR 
        NAMES onnxruntime_cxx_api.h
        PATHS 
            "${CMAKE_CURRENT_SOURCE_DIR}/../onnxruntime/include"
            "C:/onnxruntime/include"
    )
    
    find_library(ONNXRUNTIME_LIB
        NAMES onnxruntime
        PATHS 
            "${CMAKE_CURRENT_SOURCE_DIR}/../onnxruntime/lib"
            "C:/onnxruntime/lib"
    )
    
    if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIB)
        target_include_directories(test_directml PRIVATE ${ONNXRUNTIME_INCLUDE_DIR})
        target_link_libraries(test_directml ${ONNXRUNTIME_LIB})
        target_compile_definitions(test_directml PRIVATE -DRG_DIRECTML_SUPPORT)
        message("DirectML test: Using ONNX Runtime from ${ONNXRUNTIME_INCLUDE_DIR}")
    else()
        message("DirectML test: ONNX Runtime not found - DirectML tests will be disabled")
    endif()
endif()

# MSVC specific settings
if(MSVC)
    # Copy PyTorch DLLs
    file(GLOB TORCH_DLLS "${TORCH_INSTALL_PREFIX}/lib/*.dll")
    if(TORCH_DLLS)
        add_custom_command(TARGET test_directml
            POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            ${TORCH_DLLS}
            $<TARGET_FILE_DIR:test_directml>)
    endif()
    
    # Copy ONNX Runtime DLLs if available
    if(ONNXRUNTIME_LIB)
        get_filename_component(ONNXRUNTIME_LIB_DIR ${ONNXRUNTIME_LIB} DIRECTORY)
        file(GLOB ONNXRUNTIME_DLLS "${ONNXRUNTIME_LIB_DIR}/*.dll")
        if(ONNXRUNTIME_DLLS)
            add_custom_command(TARGET test_directml
                POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                ${ONNXRUNTIME_DLLS}
                $<TARGET_FILE_DIR:test_directml>)
        endif()
    endif()
endif()

# Set output directory
set_target_properties(test_directml PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}"
)
