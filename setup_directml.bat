@echo off
echo ========================================
echo GigaLearnCPP DirectML Setup Script
echo ========================================
echo.
echo This script will help you set up DirectML support for AMD GPUs
echo.

REM Check if we're in the right directory
if not exist "GigaLearnCPP\CMakeLists.txt" (
    echo ERROR: Please run this script from the GigaLearnCPP-Leak root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

REM Check if DirectML is likely supported
echo.
echo Checking for DirectML support...
powershell -Command "Get-WmiObject -Class Win32_VideoController | Where-Object {$_.Name -like '*AMD*' -or $_.Name -like '*Radeon*' -or $_.Name -like '*Intel*' -or $_.Name -like '*NVIDIA*'} | Select-Object Name"

echo.
echo ========================================
echo Installing ONNX Runtime with DirectML
echo ========================================
echo.

echo DirectML is now included in the standard ONNX Runtime packages!
echo We'll install via NuGet package manager (vcpkg) for C++.
echo.

REM Check if vcpkg is available
where vcpkg >nul 2>&1
if errorlevel 1 (
    echo Installing vcpkg package manager...
    echo.

    REM Clone vcpkg if it doesn't exist
    if not exist "vcpkg" (
        echo Cloning vcpkg...
        git clone https://github.com/Microsoft/vcpkg.git
        if errorlevel 1 (
            echo ERROR: Failed to clone vcpkg. Please install git first.
            pause
            exit /b 1
        )
    )

    REM Bootstrap vcpkg
    cd vcpkg
    call bootstrap-vcpkg.bat
    if errorlevel 1 (
        echo ERROR: Failed to bootstrap vcpkg
        cd ..
        pause
        exit /b 1
    )
    cd ..

    REM Add vcpkg to PATH for this session
    set PATH=%CD%\vcpkg;%PATH%
    echo vcpkg installed successfully!
) else (
    echo vcpkg found in PATH!
)

echo.
echo Installing ONNX Runtime with DirectML support...
echo.

REM Install ONNX Runtime via vcpkg
vcpkg install onnxruntime[directml]:x64-windows
if errorlevel 1 (
    echo WARNING: vcpkg installation failed. Trying alternative method...
    echo.
    goto manual_install
)

echo ONNX Runtime with DirectML installed successfully via vcpkg!
goto cmake_config

:manual_install
echo ========================================
echo Alternative: Manual NuGet Installation
echo ========================================
echo.
echo Since vcpkg failed, we'll set up for manual NuGet integration.
echo You'll need to use NuGet packages in your CMake configuration.
echo.

REM Create a simple packages.config for reference
echo ^<?xml version="1.0" encoding="utf-8"?^> > packages.config
echo ^<packages^> >> packages.config
echo   ^<package id="Microsoft.ML.OnnxRuntime.DirectML" version="1.22.1" targetFramework="native" /^> >> packages.config
echo ^</packages^> >> packages.config

echo Created packages.config for NuGet package reference.
echo You can use this with NuGet Package Manager or vcpkg.
echo.

:cmake_config
echo ========================================
echo Configuring CMake for DirectML
echo ========================================
echo.

REM Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: CMake not found in PATH
    echo Please install CMake and add it to your PATH
    echo Download from: https://cmake.org/download/
    echo.
) else (
    echo CMake found!
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo DirectML support has been configured for GigaLearnCPP
echo.
echo Next steps:
echo.
echo METHOD 1 - Using vcpkg (Recommended):
echo    mkdir build ^&^& cd build
echo    cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
echo    cmake --build . --config Release
echo.
echo METHOD 2 - Using NuGet packages:
echo    mkdir build ^&^& cd build
echo    cmake .. -DENABLE_DIRECTML=ON -DUSE_NUGET_PACKAGES=ON
echo    cmake --build . --config Release
echo.
echo 3. In your code, use:
echo    config.deviceType = LearnerDeviceType::GPU_DIRECTML;
echo    // or
echo    config.deviceType = LearnerDeviceType::AUTO;
echo.
echo 4. For your AMD RX 6750 XT, DirectML should provide excellent performance!
echo.
echo What was installed:
if exist "vcpkg" (
    echo - vcpkg package manager
    echo - ONNX Runtime with DirectML via vcpkg
) else (
    echo - packages.config for NuGet integration
)
echo.
echo If you encounter issues:
echo - Make sure your GPU drivers are up to date (AMD Adrenalin)
echo - Ensure Windows 10 version 1903 or later
echo - Check that your GPU supports DirectML
echo - Run tools/test_directml.exe after building to verify
echo.
pause
