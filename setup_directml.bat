@echo off
echo ========================================
echo GigaLearnCPP DirectML Setup Script
echo ========================================
echo.
echo This script will help you set up DirectML support for AMD GPUs
echo.

REM Check if we're in the right directory
if not exist "GigaLearnCPP\CMakeLists.txt" (
    echo ERROR: Please run this script from the GigaLearnCPP-Leak root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%

REM Check if DirectML is likely supported
echo.
echo Checking for DirectML support...
powershell -Command "Get-WmiObject -Class Win32_VideoController | Where-Object {$_.Name -like '*AMD*' -or $_.Name -like '*Radeon*' -or $_.Name -like '*Intel*' -or $_.Name -like '*NVIDIA*'} | Select-Object Name"

echo.
echo ========================================
echo Downloading ONNX Runtime (includes DirectML)
echo ========================================
echo.

set ONNX_VERSION=1.20.1
set ONNX_URL=https://github.com/microsoft/onnxruntime/releases/download/v%ONNX_VERSION%/onnxruntime-win-x64-%ONNX_VERSION%.zip
set ONNX_ZIP=onnxruntime-win-x64-%ONNX_VERSION%.zip
set ONNX_DIR=onnxruntime

echo DirectML support is now included in standard ONNX Runtime packages!
echo Downloading ONNX Runtime v%ONNX_VERSION% (includes DirectML)...
echo URL: %ONNX_URL%
echo.

REM Download using PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%ONNX_URL%' -OutFile '%ONNX_ZIP%'}"

if not exist "%ONNX_ZIP%" (
    echo ERROR: Failed to download ONNX Runtime
    echo Please manually download from: %ONNX_URL%
    echo Extract to: %CD%\onnxruntime\
    pause
    exit /b 1
)

echo Download completed successfully!
echo.

echo Extracting ONNX Runtime...
REM Extract using PowerShell
powershell -Command "Expand-Archive -Path '%ONNX_ZIP%' -DestinationPath 'temp_extract' -Force"

REM Move the extracted folder to the correct location
if exist "%ONNX_DIR%" rmdir /s /q "%ONNX_DIR%"
for /d %%i in (temp_extract\onnxruntime-*) do move "%%i" "%ONNX_DIR%"
rmdir /s /q temp_extract
del "%ONNX_ZIP%"

echo Extraction completed!
echo.

echo ONNX Runtime with DirectML support installed successfully!
echo.

echo ========================================
echo Configuring CMake for DirectML
echo ========================================
echo.

REM Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: CMake not found in PATH
    echo Please install CMake and add it to your PATH
    echo Download from: https://cmake.org/download/
    echo.
) else (
    echo CMake found!
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo DirectML support has been configured for GigaLearnCPP
echo.
echo Next steps:
echo 1. Build the project with CMake:
echo    mkdir build ^&^& cd build
echo    cmake .. -DENABLE_DIRECTML=ON
echo    cmake --build . --config Release
echo.
echo 2. In your code, use:
echo    config.deviceType = LearnerDeviceType::GPU_DIRECTML;
echo    // or
echo    config.deviceType = LearnerDeviceType::AUTO;
echo.
echo 3. For your AMD RX 6750 XT, DirectML should provide excellent performance!
echo.
echo Files installed:
echo - %ONNX_DIR%\include\ (ONNX Runtime headers)
echo - %ONNX_DIR%\lib\ (ONNX Runtime libraries with DirectML)
echo.
echo If you encounter issues:
echo - Make sure your GPU drivers are up to date (AMD Adrenalin)
echo - Ensure Windows 10 version 1903 or later
echo - Check that your GPU supports DirectML
echo - Run tools/test_directml.exe after building to verify
echo.
pause
