#include <iostream>
#include <chrono>
#include <vector>

#ifdef RG_DIRECTML_SUPPORT
#include <private/GigaLearnCPP/DirectMLInference.h>
#endif

#include <torch/torch.h>

/**
 * Simple DirectML test program for GigaLearnCPP
 * Tests DirectML availability and basic functionality
 */

void testDirectMLAvailability() {
    std::cout << "=== DirectML Availability Test ===" << std::endl;
    
#ifdef RG_DIRECTML_SUPPORT
    std::cout << "DirectML support compiled in: YES" << std::endl;
    
    bool available = GGL::DirectMLInference::IsDirectMLAvailable();
    std::cout << "DirectML runtime available: " << (available ? "YES" : "NO") << std::endl;
    
    if (available) {
        std::cout << "✓ DirectML is ready for use!" << std::endl;
        std::cout << "  Your AMD RX 6750 XT should be detected and usable." << std::endl;
    } else {
        std::cout << "✗ DirectML not available. Check:" << std::endl;
        std::cout << "  - Windows 10 version 1903 or later" << std::endl;
        std::cout << "  - Updated GPU drivers" << std::endl;
        std::cout << "  - ONNX Runtime with DirectML installed" << std::endl;
    }
#else
    std::cout << "DirectML support compiled in: NO" << std::endl;
    std::cout << "✗ Rebuild with -DENABLE_DIRECTML=ON" << std::endl;
#endif
    
    std::cout << std::endl;
}

void testPyTorchCUDA() {
    std::cout << "=== PyTorch CUDA Test ===" << std::endl;
    
    bool cudaAvailable = torch::cuda::is_available();
    std::cout << "PyTorch CUDA available: " << (cudaAvailable ? "YES" : "NO") << std::endl;
    
    if (cudaAvailable) {
        int deviceCount = torch::cuda::device_count();
        std::cout << "CUDA devices found: " << deviceCount << std::endl;
        
        for (int i = 0; i < deviceCount; i++) {
            auto props = torch::cuda::getDeviceProperties(i);
            std::cout << "  Device " << i << ": " << props.name << std::endl;
        }
    } else {
        std::cout << "  No CUDA devices found (expected for AMD GPU)" << std::endl;
    }
    
    std::cout << std::endl;
}

void testBasicInference() {
    std::cout << "=== Basic Inference Test ===" << std::endl;
    
#ifdef RG_DIRECTML_SUPPORT
    if (!GGL::DirectMLInference::IsDirectMLAvailable()) {
        std::cout << "Skipping inference test - DirectML not available" << std::endl;
        return;
    }
    
    try {
        // Create a simple DirectML inference instance
        auto inference = std::make_unique<GGL::DirectMLInference>();
        
        if (inference->Initialize()) {
            std::cout << "✓ DirectML inference initialized successfully" << std::endl;
            
            // Note: We can't test actual model loading without ONNX models
            // This would require converting PyTorch models to ONNX first
            std::cout << "  Ready for model loading and inference" << std::endl;
            
        } else {
            std::cout << "✗ Failed to initialize DirectML inference" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ Exception during DirectML test: " << e.what() << std::endl;
    }
#else
    std::cout << "Skipping inference test - DirectML not compiled in" << std::endl;
#endif
    
    std::cout << std::endl;
}

void testPerformanceComparison() {
    std::cout << "=== Performance Comparison Test ===" << std::endl;
    
    const int batchSize = 32;
    const int inputSize = 107; // Typical RocketLeague obs size
    const int iterations = 100;
    
    // Create test data
    auto testInput = torch::randn({batchSize, inputSize});
    
    // Test CPU performance
    auto startTime = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; i++) {
        auto result = torch::relu(torch::mm(testInput, torch::randn({inputSize, 256})));
        result = torch::mm(result, torch::randn({256, 8})); // 8 actions
        result = torch::softmax(result, -1);
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto cpuTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();
    
    std::cout << "CPU inference time: " << cpuTime / 1000.0 << " ms (" << iterations << " iterations)" << std::endl;
    std::cout << "CPU per-inference: " << cpuTime / (1000.0 * iterations) << " ms" << std::endl;
    
    // Test CUDA if available
    if (torch::cuda::is_available()) {
        testInput = testInput.to(torch::kCUDA);
        
        startTime = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < iterations; i++) {
            auto result = torch::relu(torch::mm(testInput, torch::randn({inputSize, 256}).to(torch::kCUDA)));
            result = torch::mm(result, torch::randn({256, 8}).to(torch::kCUDA));
            result = torch::softmax(result, -1);
        }
        
        torch::cuda::synchronize(); // Wait for GPU to finish
        endTime = std::chrono::high_resolution_clock::now();
        auto gpuTime = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime).count();
        
        std::cout << "CUDA inference time: " << gpuTime / 1000.0 << " ms (" << iterations << " iterations)" << std::endl;
        std::cout << "CUDA per-inference: " << gpuTime / (1000.0 * iterations) << " ms" << std::endl;
        std::cout << "CUDA speedup: " << (double)cpuTime / gpuTime << "x" << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "Note: DirectML performance will be tested once models are converted to ONNX format" << std::endl;
    std::cout << "Expected DirectML performance on RX 6750 XT: 2-5x faster than CPU" << std::endl;
    std::cout << std::endl;
}

void printSystemInfo() {
    std::cout << "=== System Information ===" << std::endl;
    std::cout << "PyTorch version: " << TORCH_VERSION << std::endl;
    std::cout << "Expected GPU: AMD RX 6750 XT" << std::endl;
    std::cout << "Expected CPU: AMD Ryzen 7 5700X" << std::endl;
    std::cout << "Target: DirectML acceleration for RL training" << std::endl;
    std::cout << std::endl;
}

int main() {
    std::cout << "GigaLearnCPP DirectML Test Suite" << std::endl;
    std::cout << "=================================" << std::endl;
    std::cout << std::endl;
    
    printSystemInfo();
    testDirectMLAvailability();
    testPyTorchCUDA();
    testBasicInference();
    testPerformanceComparison();
    
    std::cout << "=== Test Summary ===" << std::endl;
    std::cout << "If DirectML is available, you can now use:" << std::endl;
    std::cout << "  config.deviceType = LearnerDeviceType::GPU_DIRECTML;" << std::endl;
    std::cout << "  or" << std::endl;
    std::cout << "  config.deviceType = LearnerDeviceType::AUTO;" << std::endl;
    std::cout << std::endl;
    std::cout << "For best performance on your RX 6750 XT:" << std::endl;
    std::cout << "  1. Use DirectML for inference" << std::endl;
    std::cout << "  2. Consider CPU for training (more stable)" << std::endl;
    std::cout << "  3. Enable half-precision if supported" << std::endl;
    std::cout << std::endl;
    
    return 0;
}
