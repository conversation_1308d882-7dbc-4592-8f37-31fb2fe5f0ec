# DirectML Support for GigaLearnCPP

This guide explains how to enable DirectML support for AMD GPUs (like your RX 6750 XT) in GigaLearnCPP.

## What is DirectML?

DirectML is Microsoft's hardware-accelerated machine learning API that works with:
- **AMD GPUs** (RDNA, RDNA2, RDNA3) ✅ **Your RX 6750 XT**
- Intel GPUs (Arc, Iris)
- NVIDIA GPUs (as fallback)

DirectML provides excellent performance on AMD hardware where CUDA is not available.

## Quick Setup (Automated)

1. **Run the setup script:**
   ```bash
   setup_directml.bat
   ```

2. **Build with DirectML (vcpkg method):**
   ```bash
   mkdir build && cd build
   cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
   cmake --build . --config Release
   ```

3. **Use in your code:**
   ```cpp
   LearnerConfig config;
   config.deviceType = LearnerDeviceType::GPU_DIRECTML;  // Force DirectML
   // OR
   config.deviceType = LearnerDeviceType::AUTO;          // Auto-detect best device
   ```

## Manual Setup

### Prerequisites

- **Windows 10** version 1903 or later
- **Updated GPU drivers** for your RX 6750 XT
- **CMake** 3.8 or later
- **Visual Studio** 2019 or later

### Method 1: vcpkg (Recommended)

1. **Install vcpkg:**
   ```bash
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   .\bootstrap-vcpkg.bat
   ```

2. **Install ONNX Runtime with DirectML:**
   ```bash
   .\vcpkg install onnxruntime[directml]:x64-windows
   ```

3. **Build with DirectML:**
   ```bash
   mkdir build && cd build
   cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
   cmake --build . --config Release
   ```

### Method 2: NuGet Packages (Visual Studio)

1. **Install NuGet package:**
   ```bash
   dotnet add package Microsoft.ML.OnnxRuntime.DirectML
   ```

2. **Build with DirectML:**
   ```bash
   mkdir build && cd build
   cmake .. -DENABLE_DIRECTML=ON -DUSE_NUGET_PACKAGES=ON
   cmake --build . --config Release
   ```

### Step 3: Test DirectML

```bash
cd tools
mkdir build && cd build
cmake ..
cmake --build . --config Release
./test_directml.exe
```

## Performance Expectations

### Your Hardware (RX 6750 XT + Ryzen 7 5700X)

| Device | Expected Performance | Use Case |
|--------|---------------------|----------|
| **DirectML (RX 6750 XT)** | **2-5x faster than CPU** | **Inference (recommended)** |
| CPU (Ryzen 7 5700X) | Baseline | Training (stable) |
| CUDA | Not available | N/A (AMD GPU) |

### Recommended Configuration

```cpp
LearnerConfig config;
config.deviceType = LearnerDeviceType::AUTO;  // Will choose DirectML
config.ppo.useHalfPrecision = true;           // Faster on GPU
config.numGames = 300;                        // Good for your CPU
```

## Device Selection Priority

The `AUTO` device type selects in this order:
1. **CUDA** (if available - not on AMD)
2. **DirectML** (if available - ✅ your RX 6750 XT)
3. **CPU** (fallback)

## Troubleshooting

### DirectML Not Available

**Error:** `DirectML is not available`

**Solutions:**
1. **Update GPU drivers:**
   - Download latest AMD Adrenalin drivers
   - Restart after installation

2. **Check Windows version:**
   ```bash
   winver
   ```
   Must be Windows 10 1903+ or Windows 11

3. **Verify ONNX Runtime installation:**
   - vcpkg: `vcpkg list | findstr onnxruntime`
   - NuGet: Check packages in Visual Studio
   - Run `test_directml.exe` for diagnostics

### Build Errors

**Error:** `ONNX Runtime not found`

**Solutions:**
```bash
# Method 1: vcpkg
vcpkg install onnxruntime[directml]:x64-windows
cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=path/to/vcpkg.cmake

# Method 2: NuGet
dotnet add package Microsoft.ML.OnnxRuntime.DirectML
cmake .. -DENABLE_DIRECTML=ON -DUSE_NUGET_PACKAGES=ON

# Method 3: Re-run setup script
setup_directml.bat
```

**Error:** `DirectML support was not compiled in`

**Solution:**
```bash
# Clean and rebuild with correct flags
rm -rf build
mkdir build && cd build
cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake
```

### Runtime Issues

**Error:** Model inference fails

**Causes:**
1. **Model not converted to ONNX** - DirectML requires ONNX format
2. **Memory issues** - Try smaller batch sizes
3. **Driver problems** - Update AMD drivers

**Solutions:**
1. Models are automatically converted when first loaded
2. Reduce `config.numGames` if memory issues occur
3. Fallback to CPU if DirectML fails

## Advanced Configuration

### Mixed Precision Training

```cpp
config.ppo.useHalfPrecision = true;  // Faster inference on DirectML
config.deviceType = LearnerDeviceType::GPU_DIRECTML;
```

### Hybrid CPU/DirectML Setup

```cpp
// Use DirectML for inference, CPU for training
config.deviceType = LearnerDeviceType::AUTO;
// Training happens on CPU (stable)
// Inference uses DirectML (fast)
```

### Memory Optimization

```cpp
config.numGames = 200;              // Reduce if memory issues
config.ppo.batchSize = 50000;       // Adjust based on VRAM
```

## Benchmarking Your Setup

Run the test suite to verify performance:

```bash
cd tools/build
./test_directml.exe
```

Expected output for RX 6750 XT:
```
DirectML available: YES
CPU inference: ~10ms per batch
DirectML inference: ~2-4ms per batch
Speedup: 2.5-5x
```

## Integration Examples

### Basic Usage

```cpp
#include <GigaLearnCPP/Learner.h>

LearnerConfig config;
config.deviceType = LearnerDeviceType::GPU_DIRECTML;

auto learner = std::make_unique<GGL::Learner>(envCreateFn, config);
// DirectML will be used automatically for inference
```

### Inference-Only Setup

```cpp
#include <GigaLearnCPP/Util/InferUnit.h>

auto inferUnit = std::make_unique<GGL::InferUnit>(
    obsBuilder, obsSize, actionParser,
    sharedHeadConfig, policyConfig,
    modelsFolder,
    false,  // useGPU (PyTorch)
    true    // useDirectML
);
```

## FAQ

**Q: Will this work with my RX 6750 XT?**
A: Yes! RDNA2 GPUs like the RX 6750 XT have excellent DirectML support.

**Q: Should I use DirectML or CPU?**
A: Use AUTO mode - it will use DirectML for inference (fast) and CPU for training (stable).

**Q: What about CUDA?**
A: CUDA doesn't work on AMD GPUs. DirectML is the AMD equivalent.

**Q: Performance compared to NVIDIA?**
A: DirectML on RX 6750 XT should be competitive with mid-range NVIDIA GPUs for inference.

**Q: Can I use both DirectML and CPU?**
A: Yes! The hybrid approach uses DirectML for inference and CPU for training.

## Support

If you encounter issues:

1. **Run diagnostics:** `tools/build/test_directml.exe`
2. **Check logs** for DirectML initialization messages
3. **Update drivers** - AMD Adrenalin Software
4. **Verify Windows version** - Must be 1903+

Your RX 6750 XT should provide excellent performance with DirectML! 🚀
