#pragma once
#include "Reward.h"

namespace RLGC {
	// C++ version of your JumpTouchReward from customreward.py
	// Rewards touching the ball in the air, scaled by height.
	class JumpTouchReward : public Reward {
	public:
		constexpr static float MIN_HEIGHT = 92.75f; // Ball radius
		constexpr static float MAX_HEIGHT = 2044.0f - 92.75f;
		constexpr static float RANGE = MAX_HEIGHT - MIN_HEIGHT;

		virtual float GetReward(const Player& player, const GameState& state, bool isFinal) override {
			if (player.ballTouchedStep && !player.isOnGround && state.ball.pos.z >= MIN_HEIGHT) {
				// Scale the reward from 0 to 1 based on the height of the touch
				return (state.ball.pos.z - MIN_HEIGHT) / RANGE;
			}
			return 0;
		}
	};
}