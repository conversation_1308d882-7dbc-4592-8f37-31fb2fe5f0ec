cmake_minimum_required (VERSION 3.8)

project("GigaLearnCPP")

include_directories("${PROJECT_SOURCE_DIR}/src/")

# Make sure CMake finds libtorch if its in this directory
if (EXISTS "${PROJECT_SOURCE_DIR}/libtorch/")
	message("Using local libtorch folder...")
	list(APPEND CMAKE_PREFIX_PATH "libtorch")
	# Make ultra-sure we can find libtorch if its local
	set(CMAKE_PREFIX_PATH "libtorch/share/cmake/Torch")
endif()

# Add libtorch (https://pytorch.org/cppdocs/installing.html#minimal-example)
find_package(Torch REQUIRED)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${TORCH_CXX_FLAGS}")

# Add all headers and code files
file(GLOB_RECURSE FILES_SRC "src/*.cpp" "src/*.h" "src/*.hpp" "libsrc/*.cpp" "libsrc/.h" "libsrc/.hpp")

add_library(GigaLearnCPP SHARED ${FILES_SRC})
target_compile_definitions(GigaLearnCPP PRIVATE -DWITHIN_GGL)
target_include_directories(GigaLearnCPP PUBLIC "src/public")
target_include_directories(GigaLearnCPP PRIVATE "src/private")

# Include libtorch
target_link_libraries(GigaLearnCPP PRIVATE "${TORCH_LIBRARIES}")

if (TORCH_CUDA_LIBRARIES)
	message("Enabling CUDA support...")
	target_compile_definitions(GigaLearnCPP PRIVATE -DRG_CUDA_SUPPORT)
endif()

# DirectML support via ONNX Runtime
option(ENABLE_DIRECTML "Enable DirectML support for AMD GPUs" ON)

if(ENABLE_DIRECTML)
	message("Searching for ONNX Runtime with DirectML...")

	# Try to find ONNX Runtime
	find_path(ONNXRUNTIME_INCLUDE_DIR
		NAMES onnxruntime_cxx_api.h
		PATHS
			"${PROJECT_SOURCE_DIR}/onnxruntime/include"
			"${CMAKE_CURRENT_SOURCE_DIR}/onnxruntime/include"
			"C:/Program Files/onnxruntime/include"
			"C:/onnxruntime/include"
		DOC "ONNX Runtime include directory"
	)

	find_library(ONNXRUNTIME_LIB
		NAMES onnxruntime
		PATHS
			"${PROJECT_SOURCE_DIR}/onnxruntime/lib"
			"${CMAKE_CURRENT_SOURCE_DIR}/onnxruntime/lib"
			"C:/Program Files/onnxruntime/lib"
			"C:/onnxruntime/lib"
		DOC "ONNX Runtime library"
	)

	if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIB)
		message("Found ONNX Runtime:")
		message("  Include: ${ONNXRUNTIME_INCLUDE_DIR}")
		message("  Library: ${ONNXRUNTIME_LIB}")

		target_include_directories(GigaLearnCPP PRIVATE ${ONNXRUNTIME_INCLUDE_DIR})
		target_link_libraries(GigaLearnCPP PRIVATE ${ONNXRUNTIME_LIB})
		target_compile_definitions(GigaLearnCPP PRIVATE -DRG_DIRECTML_SUPPORT)

		# Copy ONNX Runtime DLLs on Windows
		if(MSVC)
			get_filename_component(ONNXRUNTIME_LIB_DIR ${ONNXRUNTIME_LIB} DIRECTORY)
			file(GLOB ONNXRUNTIME_DLLS "${ONNXRUNTIME_LIB_DIR}/*.dll")
			if(ONNXRUNTIME_DLLS)
				message("Adding ONNX Runtime DLLs: ${ONNXRUNTIME_DLLS}")
				add_custom_command(TARGET GigaLearnCPP
					POST_BUILD
					COMMAND ${CMAKE_COMMAND} -E copy_if_different
					${ONNXRUNTIME_DLLS}
					$<TARGET_FILE_DIR:GigaLearnCPP>)
			endif()
		endif()
	else()
		message(WARNING "ONNX Runtime not found. DirectML support will be disabled.")
		message("To enable DirectML:")
		message("1. Download ONNX Runtime with DirectML from: https://github.com/microsoft/onnxruntime/releases")
		message("2. Extract to: ${PROJECT_SOURCE_DIR}/onnxruntime/")
		message("3. Or install to: C:/onnxruntime/")
	endif()
endif()

# Set C++ version to 20
set_target_properties(GigaLearnCPP PROPERTIES LINKER_LANGUAGE CXX)
set_target_properties(GigaLearnCPP PROPERTIES CXX_STANDARD 20)

# Include RLGymCPP
add_subdirectory(RLGymCPP)
target_link_libraries(GigaLearnCPP PUBLIC RLGymCPP)

# Include JSON
target_include_directories(GigaLearnCPP PUBLIC "${PROJECT_SOURCE_DIR}/libsrc/json")

# Include python
find_package(Python COMPONENTS Interpreter Development)
find_package(PythonLibs REQUIRED)
include_directories(${PYTHON_INCLUDE_DIRS})
target_link_libraries(GigaLearnCPP PUBLIC ${PYTHON_LIBRARIES})
message("Found Python:")
message("PYTHON_LIBRARIES: ${PYTHON_LIBRARIES}")
message("PYTHON_INCLUDE_DIRS: ${PYTHON_INCLUDE_DIRS}")
message("Python_RUNTIME_LIBRARY_DIRS: ${Python_RUNTIME_LIBRARY_DIRS}")
message("Python_EXECUTABLE: ${Python_EXECUTABLE}")
add_definitions(-DPY_EXEC_PATH="${Python_EXECUTABLE}") # Give C++ access to the executable path

# Include pybind11
add_subdirectory(pybind11)
target_link_libraries(GigaLearnCPP PUBLIC pybind11::embed)

# MSVC fails to find python DLLs even through they are in my path. Good job MSVC. Well done.
# This copies the the python DLLs to the output directory
if (MSVC)
    file(GLOB PYTHON_DLLS "${Python_RUNTIME_LIBRARY_DIRS}/*.dll")
	message("Adding Python DLLS: ${PYTHON_DLLS}")
    add_custom_command(TARGET GigaLearnCPP
                 POST_BUILD
                 COMMAND ${CMAKE_COMMAND} -E copy_if_different
                 ${PYTHON_DLLS}
                 $<TARGET_FILE_DIR:GigaLearnCPP>)
endif (MSVC)

# Make our python files copy over to our build dir
configure_file("./python_scripts/metric_receiver.py" "../python_scripts/metric_receiver.py" COPY)
configure_file("./python_scripts/render_receiver.py" "../python_scripts/render_receiver.py" COPY)

# MSVC sometimes won't link to the libtorch DLLs unless you do this
# This is also from https://pytorch.org/cppdocs/installing.html#minimal-example
if (MSVC)
    file(GLOB TORCH_DLLS "${TORCH_INSTALL_PREFIX}/lib/*.dll")
	message("Adding TORCH_DLLS: ${TORCH_DLLS}")
    add_custom_command(TARGET GigaLearnCPP
                 POST_BUILD
                 COMMAND ${CMAKE_COMMAND} -E copy_if_different
                 ${TORCH_DLLS}
                 $<TARGET_FILE_DIR:GigaLearnCPP>)
endif (MSVC)