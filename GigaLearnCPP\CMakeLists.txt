cmake_minimum_required (VERSION 3.8)

project("GigaLearnCPP")

include_directories("${PROJECT_SOURCE_DIR}/src/")

# Make sure CMake finds libtorch if its in this directory
if (EXISTS "${PROJECT_SOURCE_DIR}/libtorch/")
	message("Using local libtorch folder...")
	list(APPEND CMAKE_PREFIX_PATH "libtorch")
	# Make ultra-sure we can find libtorch if its local
	set(CMAKE_PREFIX_PATH "libtorch/share/cmake/Torch")
endif()

# Add libtorch (https://pytorch.org/cppdocs/installing.html#minimal-example)
find_package(Torch REQUIRED)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${TORCH_CXX_FLAGS}")

# Add all headers and code files
file(GLOB_RECURSE FILES_SRC "src/*.cpp" "src/*.h" "src/*.hpp" "libsrc/*.cpp" "libsrc/.h" "libsrc/.hpp")

add_library(GigaLearnCPP SHARED ${FILES_SRC})
target_compile_definitions(GigaLearnCPP PRIVATE -DWITHIN_GGL)
target_include_directories(GigaLearnCPP PUBLIC "src/public")
target_include_directories(GigaLearnCPP PRIVATE "src/private")

# Include libtorch
target_link_libraries(GigaLearnCPP PRIVATE "${TORCH_LIBRARIES}")

if (TORCH_CUDA_LIBRARIES)
	message("Enabling CUDA support...")
	target_compile_definitions(GigaLearnCPP PRIVATE -DRG_CUDA_SUPPORT)
endif()

# DirectML support via ONNX Runtime
option(ENABLE_DIRECTML "Enable DirectML support for AMD GPUs" ON)
option(USE_NUGET_PACKAGES "Use NuGet packages for ONNX Runtime" OFF)

if(ENABLE_DIRECTML)
	message("Searching for ONNX Runtime with DirectML...")

	# Method 1: Try vcpkg first (recommended)
	if(NOT USE_NUGET_PACKAGES)
		find_package(onnxruntime CONFIG QUIET)
		if(onnxruntime_FOUND)
			message("Found ONNX Runtime via vcpkg")
			target_link_libraries(GigaLearnCPP PRIVATE onnxruntime::onnxruntime)
			target_compile_definitions(GigaLearnCPP PRIVATE -DRG_DIRECTML_SUPPORT)
			set(DIRECTML_FOUND TRUE)
		endif()
	endif()

	# Method 2: Try manual paths if vcpkg failed
	if(NOT DIRECTML_FOUND)
		find_path(ONNXRUNTIME_INCLUDE_DIR
			NAMES onnxruntime_cxx_api.h
			PATHS
				"${PROJECT_SOURCE_DIR}/onnxruntime/include"
				"${CMAKE_CURRENT_SOURCE_DIR}/onnxruntime/include"
				"C:/Program Files/onnxruntime/include"
				"C:/onnxruntime/include"
				# vcpkg paths
				"${CMAKE_CURRENT_SOURCE_DIR}/vcpkg/installed/x64-windows/include"
				"${VCPKG_INSTALLED_DIR}/x64-windows/include"
			DOC "ONNX Runtime include directory"
		)

		find_library(ONNXRUNTIME_LIB
			NAMES onnxruntime
			PATHS
				"${PROJECT_SOURCE_DIR}/onnxruntime/lib"
				"${CMAKE_CURRENT_SOURCE_DIR}/onnxruntime/lib"
				"C:/Program Files/onnxruntime/lib"
				"C:/onnxruntime/lib"
				# vcpkg paths
				"${CMAKE_CURRENT_SOURCE_DIR}/vcpkg/installed/x64-windows/lib"
				"${VCPKG_INSTALLED_DIR}/x64-windows/lib"
			DOC "ONNX Runtime library"
		)

		if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIB)
			message("Found ONNX Runtime:")
			message("  Include: ${ONNXRUNTIME_INCLUDE_DIR}")
			message("  Library: ${ONNXRUNTIME_LIB}")

			target_include_directories(GigaLearnCPP PRIVATE ${ONNXRUNTIME_INCLUDE_DIR})
			target_link_libraries(GigaLearnCPP PRIVATE ${ONNXRUNTIME_LIB})
			target_compile_definitions(GigaLearnCPP PRIVATE -DRG_DIRECTML_SUPPORT)

			# Copy ONNX Runtime DLLs on Windows
			if(MSVC)
				get_filename_component(ONNXRUNTIME_LIB_DIR ${ONNXRUNTIME_LIB} DIRECTORY)
				file(GLOB ONNXRUNTIME_DLLS "${ONNXRUNTIME_LIB_DIR}/../bin/*.dll")
				if(NOT ONNXRUNTIME_DLLS)
					file(GLOB ONNXRUNTIME_DLLS "${ONNXRUNTIME_LIB_DIR}/*.dll")
				endif()
				if(ONNXRUNTIME_DLLS)
					message("Adding ONNX Runtime DLLs: ${ONNXRUNTIME_DLLS}")
					add_custom_command(TARGET GigaLearnCPP
						POST_BUILD
						COMMAND ${CMAKE_COMMAND} -E copy_if_different
						${ONNXRUNTIME_DLLS}
						$<TARGET_FILE_DIR:GigaLearnCPP>)
				endif()
			endif()
			set(DIRECTML_FOUND TRUE)
		endif()
	endif()

	# Method 3: NuGet package integration (for Visual Studio)
	if(NOT DIRECTML_FOUND AND USE_NUGET_PACKAGES)
		message("Attempting NuGet package integration...")
		message("Please ensure Microsoft.ML.OnnxRuntime.DirectML NuGet package is installed")
		message("You can install it via:")
		message("  - Visual Studio Package Manager")
		message("  - dotnet add package Microsoft.ML.OnnxRuntime.DirectML")
		message("  - Or add to packages.config")

		# Try to find NuGet packages directory
		find_path(NUGET_PACKAGES_DIR
			NAMES "microsoft.ml.onnxruntime.directml"
			PATHS
				"${CMAKE_CURRENT_SOURCE_DIR}/packages"
				"$ENV{USERPROFILE}/.nuget/packages"
				"C:/Users/<USER>/.nuget/packages"
			PATH_SUFFIXES "microsoft.ml.onnxruntime.directml"
		)

		if(NUGET_PACKAGES_DIR)
			message("Found NuGet packages directory: ${NUGET_PACKAGES_DIR}")
			# This would need more specific version handling for production use
			target_compile_definitions(GigaLearnCPP PRIVATE -DRG_DIRECTML_SUPPORT)
			set(DIRECTML_FOUND TRUE)
		endif()
	endif()

	if(NOT DIRECTML_FOUND)
		message(WARNING "ONNX Runtime with DirectML not found. DirectML support will be disabled.")
		message("To enable DirectML, choose one of these methods:")
		message("1. VCPKG (Recommended):")
		message("   vcpkg install onnxruntime[directml]:x64-windows")
		message("   cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=path/to/vcpkg.cmake")
		message("2. NuGet packages:")
		message("   Install Microsoft.ML.OnnxRuntime.DirectML via NuGet")
		message("   cmake .. -DENABLE_DIRECTML=ON -DUSE_NUGET_PACKAGES=ON")
		message("3. Manual installation:")
		message("   Run setup_directml.bat for automated setup")
	endif()
endif()

# Set C++ version to 20
set_target_properties(GigaLearnCPP PROPERTIES LINKER_LANGUAGE CXX)
set_target_properties(GigaLearnCPP PROPERTIES CXX_STANDARD 20)

# Include RLGymCPP
add_subdirectory(RLGymCPP)
target_link_libraries(GigaLearnCPP PUBLIC RLGymCPP)

# Include JSON
target_include_directories(GigaLearnCPP PUBLIC "${PROJECT_SOURCE_DIR}/libsrc/json")

# Include python
find_package(Python COMPONENTS Interpreter Development)
find_package(PythonLibs REQUIRED)
include_directories(${PYTHON_INCLUDE_DIRS})
target_link_libraries(GigaLearnCPP PUBLIC ${PYTHON_LIBRARIES})
message("Found Python:")
message("PYTHON_LIBRARIES: ${PYTHON_LIBRARIES}")
message("PYTHON_INCLUDE_DIRS: ${PYTHON_INCLUDE_DIRS}")
message("Python_RUNTIME_LIBRARY_DIRS: ${Python_RUNTIME_LIBRARY_DIRS}")
message("Python_EXECUTABLE: ${Python_EXECUTABLE}")
add_definitions(-DPY_EXEC_PATH="${Python_EXECUTABLE}") # Give C++ access to the executable path

# Include pybind11
add_subdirectory(pybind11)
target_link_libraries(GigaLearnCPP PUBLIC pybind11::embed)

# MSVC fails to find python DLLs even through they are in my path. Good job MSVC. Well done.
# This copies the the python DLLs to the output directory
if (MSVC)
    file(GLOB PYTHON_DLLS "${Python_RUNTIME_LIBRARY_DIRS}/*.dll")
	message("Adding Python DLLS: ${PYTHON_DLLS}")
    add_custom_command(TARGET GigaLearnCPP
                 POST_BUILD
                 COMMAND ${CMAKE_COMMAND} -E copy_if_different
                 ${PYTHON_DLLS}
                 $<TARGET_FILE_DIR:GigaLearnCPP>)
endif (MSVC)

# Make our python files copy over to our build dir
configure_file("./python_scripts/metric_receiver.py" "../python_scripts/metric_receiver.py" COPY)
configure_file("./python_scripts/render_receiver.py" "../python_scripts/render_receiver.py" COPY)

# MSVC sometimes won't link to the libtorch DLLs unless you do this
# This is also from https://pytorch.org/cppdocs/installing.html#minimal-example
if (MSVC)
    file(GLOB TORCH_DLLS "${TORCH_INSTALL_PREFIX}/lib/*.dll")
	message("Adding TORCH_DLLS: ${TORCH_DLLS}")
    add_custom_command(TARGET GigaLearnCPP
                 POST_BUILD
                 COMMAND ${CMAKE_COMMAND} -E copy_if_different
                 ${TORCH_DLLS}
                 $<TARGET_FILE_DIR:GigaLearnCPP>)
endif (MSVC)