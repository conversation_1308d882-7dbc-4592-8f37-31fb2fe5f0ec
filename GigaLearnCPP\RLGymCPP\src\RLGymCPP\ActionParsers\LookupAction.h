#pragma once
#include "ActionParser.h"

namespace RLGC {
	// A corrected version of LookupAction that matches your GigaLearn API
	class LookupAction : public ActionParser {
	private:
		std::vector<Action> _lookupTable;

	public:
		LookupAction() {
			_buildLookupTable();
		}

		// Corrected function signature
		virtual Action ParseAction(int index, const Player& player, const GameState& state) override {
			// Basic bounds check to prevent crashes
			if (index < 0 || index >= _lookupTable.size()) {
				return Action(); // Return a default, zeroed-out action
			}
			return _lookupTable[index];
		}
		
		// Corrected function signature
		virtual int GetActionAmount() override {
			return _lookupTable.size();
		}

	private:
		void _buildLookupTable() {
			// This logic is unchanged as it's correct
			constexpr float
				R_B[] = { 0, 1 },
				R_F[] = { -1, 0, 1 };

			// Ground
			for (float throttle : R_F) {
				for (float steer : R_F) {
					for (float boost : R_B) {
						for (float handbrake : R_B) {
							if (boost == 1 && throttle != 1) continue;

							Action action = {};
							action.throttle = throttle;
							action.steer = steer;
							action.yaw = steer;
							if (boost == 1) action.throttle = 1;
							action.boost = boost;
							action.handbrake = handbrake;
							_lookupTable.push_back(action);
						}
					}
				}
			}
			// Aerial
			for (float pitch : R_F) {
				for (float yaw : R_F) {
					for (float roll : R_F) {
						for (float jump : R_B) {
							for (float boost : R_B) {
								if (jump == 1 && yaw != 0) continue;
								if (pitch == 0 && roll == 0 && jump == 0) continue;
								
								Action action = {};
								action.throttle = boost;
								action.pitch = pitch;
								action.yaw = yaw;
								action.roll = roll;
								action.jump = jump;
								action.boost = boost;
								action.handbrake = (jump == 1 && (pitch != 0 || yaw != 0 || roll != 0));
								_lookupTable.push_back(action);
							}
						}
					}
				}
			}
		}
	};
}