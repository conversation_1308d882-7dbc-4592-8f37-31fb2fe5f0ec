@echo off
echo ========================================
echo Simple DirectML Setup for GigaLearnCPP
echo ========================================
echo.
echo This downloads ONNX Runtime 1.22.2 directly from GitHub
echo (DirectML support is included in standard packages)
echo.

REM Check if we're in the right directory
if not exist "GigaLearnCPP\CMakeLists.txt" (
    echo ERROR: Please run this script from the GigaLearnCPP-Leak root directory
    pause
    exit /b 1
)

echo Downloading ONNX Runtime 1.20.1 with DirectML support...
echo.

set DOWNLOAD_URL=https://github.com/microsoft/onnxruntime/releases/download/v1.20.1/onnxruntime-win-x64-1.20.1.zip
set ZIP_FILE=onnxruntime-win-x64-1.20.1.zip
set EXTRACT_DIR=onnxruntime

echo URL: %DOWNLOAD_URL%
echo.

REM Download the file
echo Downloading...
powershell -Command "Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%ZIP_FILE%'"

if not exist "%ZIP_FILE%" (
    echo ERROR: Download failed!
    echo Please manually download from: %DOWNLOAD_URL%
    pause
    exit /b 1
)

echo Download successful!
echo.

REM Clean up old installation
if exist "%EXTRACT_DIR%" (
    echo Removing old installation...
    rmdir /s /q "%EXTRACT_DIR%"
)

echo Extracting...
powershell -Command "Expand-Archive -Path '%ZIP_FILE%' -DestinationPath 'temp_extract' -Force"

REM Move the extracted folder
for /d %%i in (temp_extract\onnxruntime-*) do (
    move "%%i" "%EXTRACT_DIR%"
)

REM Clean up
rmdir /s /q temp_extract
del "%ZIP_FILE%"

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.

REM Verify installation
if exist "%EXTRACT_DIR%\include\onnxruntime_cxx_api.h" (
    echo ✓ Headers found: %EXTRACT_DIR%\include\
) else (
    echo ✗ Headers not found!
)

if exist "%EXTRACT_DIR%\lib\onnxruntime.lib" (
    echo ✓ Library found: %EXTRACT_DIR%\lib\
) else (
    echo ✗ Library not found!
)

if exist "%EXTRACT_DIR%\lib\onnxruntime.dll" (
    echo ✓ DLL found: %EXTRACT_DIR%\lib\
) else (
    echo ✗ DLL not found!
)

echo.
echo DirectML support is included in this ONNX Runtime package!
echo.
echo Next steps:
echo 1. Build the project:
echo    mkdir build ^&^& cd build
echo    cmake .. -DENABLE_DIRECTML=ON
echo    cmake --build . --config Release
echo.
echo 2. In your code:
echo    config.deviceType = LearnerDeviceType::GPU_DIRECTML;
echo.
echo 3. Test DirectML:
echo    cd tools\build
echo    test_directml.exe
echo.
echo Your AMD RX 6750 XT should work perfectly with DirectML!
echo.
pause
