/**
 * DirectML Example for GigaLearnCPP
 * 
 * This example shows how to use DirectML acceleration with your AMD RX 6750 XT
 * for Rocket League bot training and inference.
 */

#include <GigaLearnCPP/Learner.h>
#include <GigaLearnCPP/Util/InferUnit.h>
#include <iostream>

// Example environment creation function
// (You would replace this with your actual environment setup)
RLGC::EnvCreateResult CreateExampleEnv(int index) {
    // This is a placeholder - implement your actual environment creation
    RLGC::EnvCreateResult result = {};
    
    // Create arena, rewards, obs builder, etc.
    // result.arena = new Arena(...);
    // result.obsBuilder = new YourObsBuilder();
    // result.actionParser = new YourActionParser();
    // etc.
    
    return result;
}

void DirectMLTrainingExample() {
    std::cout << "=== DirectML Training Example ===" << std::endl;
    
    // Configure learner for DirectML
    GGL::LearnerConfig config;
    
    // Device configuration for your AMD RX 6750 XT
    config.deviceType = GGL::LearnerDeviceType::AUTO;  // Will auto-select DirectML
    // OR explicitly use DirectML:
    // config.deviceType = GGL::LearnerDeviceType::GPU_DIRECTML;
    
    // Optimize for your hardware
    config.numGames = 250;  // Good balance for Ryzen 7 5700X
    config.ppo.useHalfPrecision = true;  // Faster on DirectML
    
    // PPO configuration optimized for DirectML
    config.ppo.batchSize = 50000;
    config.ppo.miniBatchSize = 25000;
    config.ppo.epochs = 3;
    
    // Model architecture (adjust based on your needs)
    config.ppo.policy.layerSizes = {256, 256, 256};
    config.ppo.critic.layerSizes = {256, 256, 256};
    config.ppo.sharedHead.layerSizes = {128, 128};
    
    // Enable checkpointing
    config.checkpointFolder = "checkpoints_directml";
    config.tsPerSave = 1000000;  // Save every 1M timesteps
    
    try {
        // Create learner with DirectML support
        auto learner = std::make_unique<GGL::Learner>(
            CreateExampleEnv,  // Your environment creation function
            config,
            nullptr  // Optional step callback
        );
        
        std::cout << "✓ Learner created with DirectML support" << std::endl;
        std::cout << "  Device: " << (config.deviceType == GGL::LearnerDeviceType::AUTO ? "AUTO (DirectML preferred)" : "DirectML") << std::endl;
        std::cout << "  Games: " << config.numGames << std::endl;
        std::cout << "  Half precision: " << (config.ppo.useHalfPrecision ? "Enabled" : "Disabled") << std::endl;
        
        // Start training
        // learner->Learn();  // Uncomment when you have a real environment
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error creating learner: " << e.what() << std::endl;
        std::cout << "  Check DirectML setup and try CPU fallback" << std::endl;
    }
}

void DirectMLInferenceExample() {
    std::cout << "\n=== DirectML Inference Example ===" << std::endl;
    
    // Model configuration for inference
    GGL::PartialModelConfig sharedHeadConfig;
    sharedHeadConfig.layerSizes = {128, 128};
    
    GGL::PartialModelConfig policyConfig;
    policyConfig.layerSizes = {256, 256, 256};
    
    try {
        // Create inference unit with DirectML
        auto inferUnit = std::make_unique<GGL::InferUnit>(
            nullptr,  // Your obs builder
            107,      // Typical RL obs size
            nullptr,  // Your action parser
            sharedHeadConfig,
            policyConfig,
            "checkpoints_directml/latest",  // Model path
            false,    // useGPU (PyTorch CUDA)
            true      // useDirectML
        );
        
        std::cout << "✓ Inference unit created with DirectML" << std::endl;
        std::cout << "  Optimized for real-time bot inference" << std::endl;
        std::cout << "  Expected latency on RX 6750 XT: 1-3ms per inference" << std::endl;
        
        // Example inference (when you have real data)
        // std::vector<RLGC::Player> players = {...};
        // std::vector<RLGC::GameState> states = {...};
        // auto actions = inferUnit->BatchInferActions(players, states, true, 1.0f);
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error creating inference unit: " << e.what() << std::endl;
    }
}

void PerformanceOptimizationTips() {
    std::cout << "\n=== Performance Tips for RX 6750 XT ===" << std::endl;
    
    std::cout << "1. Memory Management:" << std::endl;
    std::cout << "   - RX 6750 XT has 12GB VRAM - plenty for RL models" << std::endl;
    std::cout << "   - Use batch sizes up to 100k for training" << std::endl;
    std::cout << "   - Enable half precision for 2x memory efficiency" << std::endl;
    
    std::cout << "\n2. Training Strategy:" << std::endl;
    std::cout << "   - Use DirectML for inference (fast)" << std::endl;
    std::cout << "   - Consider CPU for training (more stable)" << std::endl;
    std::cout << "   - Your Ryzen 7 5700X handles 200-300 games well" << std::endl;
    
    std::cout << "\n3. Model Architecture:" << std::endl;
    std::cout << "   - 256-512 hidden units work well on DirectML" << std::endl;
    std::cout << "   - 3-4 layers provide good performance/speed balance" << std::endl;
    std::cout << "   - Shared heads reduce computation" << std::endl;
    
    std::cout << "\n4. Inference Optimization:" << std::endl;
    std::cout << "   - Batch multiple inferences when possible" << std::endl;
    std::cout << "   - Use deterministic mode for deployment" << std::endl;
    std::cout << "   - Cache models to avoid reload overhead" << std::endl;
}

void HybridCPUDirectMLExample() {
    std::cout << "\n=== Hybrid CPU/DirectML Setup ===" << std::endl;
    
    GGL::LearnerConfig config;
    
    // This setup uses:
    // - CPU for training (stable, good on Ryzen 7 5700X)
    // - DirectML for inference (fast on RX 6750 XT)
    config.deviceType = GGL::LearnerDeviceType::AUTO;
    
    // CPU-optimized training settings
    config.numGames = 300;  // Your CPU can handle this
    config.ppo.batchSize = 50000;
    config.ppo.miniBatchSize = 25000;
    
    // DirectML will be used automatically for inference
    config.ppo.useHalfPrecision = true;  // Benefits DirectML inference
    
    std::cout << "✓ Hybrid setup configured:" << std::endl;
    std::cout << "  Training: CPU (Ryzen 7 5700X)" << std::endl;
    std::cout << "  Inference: DirectML (RX 6750 XT)" << std::endl;
    std::cout << "  Best of both worlds!" << std::endl;
}

int main() {
    std::cout << "GigaLearnCPP DirectML Examples" << std::endl;
    std::cout << "=============================" << std::endl;
    std::cout << "Optimized for AMD RX 6750 XT + Ryzen 7 5700X" << std::endl;
    
    DirectMLTrainingExample();
    DirectMLInferenceExample();
    PerformanceOptimizationTips();
    HybridCPUDirectMLExample();
    
    std::cout << "\n=== Next Steps ===" << std::endl;
    std::cout << "1. Run setup_directml.bat to install DirectML" << std::endl;
    std::cout << "2. Build with: cmake .. -DENABLE_DIRECTML=ON" << std::endl;
    std::cout << "3. Test with: tools/test_directml.exe" << std::endl;
    std::cout << "4. Integrate DirectML into your training pipeline" << std::endl;
    std::cout << "\nEnjoy faster RL training on your AMD hardware! 🚀" << std::endl;
    
    return 0;
}
