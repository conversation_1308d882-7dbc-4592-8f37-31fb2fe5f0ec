#include "InferUnit.h"

#include <GigaLearnCPP/Util/Models.h>
#include <GigaLearnCPP/PPO/PPOLearner.h>

#ifdef RG_DIRECTML_SUPPORT
#include <private/GigaLearnCPP/DirectMLInference.h>
#endif

GGL::InferUnit::InferUnit(
	RLGC::ObsBuilder* obsBuilder, int obsSize, RLGC::ActionParser* actionParser,
	PartialModelConfig sharedHeadConfig, PartialModelConfig policyConfig,
	std::filesystem::path modelsFolder, bool useGPU, bool useDirectML) :
	obsBuilder(obsBuilder), obsSize(obsSize), actionParser(actionParser), useGPU(useGPU) {

#ifdef RG_DIRECTML_SUPPORT
	this->useDirectML = useDirectML;
	if (useDirectML) {
		// Generate a unique model key for this configuration
		this->modelKey = "infer_" + std::to_string(obsSize) + "_" + std::to_string(actionParser->GetActionAmount());

		// Check if DirectML models are available
		if (g_directMLManager.IsModelAvailable(modelKey)) {
			this->directMLInference = g_directMLManager.GetInference(modelKey);
			if (this->directMLInference) {
				RG_LOG("InferUnit: Using DirectML inference for model: " << modelKey);
				// Still create PyTorch models for fallback
				this->models = new ModelSet();
				try {
					PPOLearner::MakeModels(
						false, obsSize, actionParser->GetActionAmount(),
						sharedHeadConfig, policyConfig, {},
						torch::kCPU, // Use CPU for PyTorch when DirectML is primary
						*this->models
					);
					this->models->Load(modelsFolder, false, false);
				} catch (std::exception& e) {
					RG_LOG("InferUnit: Warning - Failed to load PyTorch fallback models: " << e.what());
				}
				return;
			}
		}

		// DirectML not available, fall back to regular GPU/CPU
		RG_LOG("InferUnit: DirectML models not available, falling back to PyTorch");
		this->useDirectML = false;
	}
#endif

	this->models = new ModelSet();

	try {
		PPOLearner::MakeModels(
			false, obsSize, actionParser->GetActionAmount(),
			sharedHeadConfig, policyConfig, {},
			useGPU ? torch::kCUDA : torch::kCPU,
			*this->models
		);
	} catch (std::exception& e) {
		RG_ERR_CLOSE("InferUnit: Exception when trying to construct models: " << e.what());
	}

	try {
		this->models->Load(modelsFolder, false, false);
	} catch (std::exception& e) {
		RG_ERR_CLOSE("InferUnit: Exception when trying to load models: " << e.what());
	}
}

RLGC::Action GGL::InferUnit::InferAction(const RLGC::Player& player, const RLGC::GameState& state, bool deterministic, float temperature) {
	return BatchInferActions({ player }, { state }, deterministic, temperature)[0];
}

std::vector<RLGC::Action> GGL::InferUnit::BatchInferActions(const std::vector<RLGC::Player>& players, const std::vector<RLGC::GameState>& states, bool deterministic, float temperature) {
	RG_ASSERT(players.size() > 0 && states.size() > 0);
	RG_ASSERT(players.size() == states.size());

	int batchSize = players.size();
	std::vector<float> allObs;
	std::vector<uint8_t> allActionMasks;
	for (int i = 0; i < batchSize; i++) {
		FList curObs = obsBuilder->BuildObs(players[i], states[i]);
		if (curObs.size() != obsSize) {
			RG_ERR_CLOSE(
				"InferUnit: Obs builder produced an obs that differs from the provided size (expected: " << obsSize << ", got: " << curObs.size() << ")\n" <<
				"Make sure you provided the correct obs size to the InferUnit constructor.\n" <<
				"Also, make sure there aren't an incorrect number of players (there are " << states[i].players.size() << " in this state)"
			);
		}
		allObs += curObs;

		allActionMasks += actionParser->GetActionMask(players[i], states[i]);
	}
	
	std::vector<RLGC::Action> results = {};

	try {
		RG_NO_GRAD;

#ifdef RG_DIRECTML_SUPPORT
		// Try DirectML inference first if available
		if (useDirectML && directMLInference) {
			auto tObs = torch::tensor(allObs).reshape({(int64_t)players.size(), obsSize});
			auto tActionMasks = torch::tensor(allActionMasks).reshape({(int64_t)players.size(), this->actionParser->GetActionAmount()});

			// Convert to DirectML format and run inference
			std::vector<torch::Tensor> inputs = {tObs, tActionMasks};
			auto directMLResults = directMLInference->RunInferenceFromTorch(inputs);

			if (!directMLResults.empty() && directMLResults[0].size() >= players.size()) {
				// Convert DirectML results back to action indices
				std::vector<int> actionIndices;
				for (size_t i = 0; i < players.size(); i++) {
					// Find the action with highest probability (argmax)
					float maxProb = -1.0f;
					int maxIdx = 0;
					size_t startIdx = i * actionParser->GetActionAmount();

					for (int j = 0; j < actionParser->GetActionAmount(); j++) {
						if (startIdx + j < directMLResults[0].size()) {
							float prob = directMLResults[0][startIdx + j];
							if (prob > maxProb) {
								maxProb = prob;
								maxIdx = j;
							}
						}
					}
					actionIndices.push_back(maxIdx);
				}

				for (int i = 0; i < batchSize; i++)
					results.push_back(actionParser->ParseAction(actionIndices[i], players[i], states[i]));

				return results;
			} else {
				RG_LOG("InferUnit: DirectML inference failed, falling back to PyTorch");
			}
		}
#endif

		// Fallback to PyTorch inference
		auto device = useGPU ? torch::kCUDA : torch::kCPU;

		auto tObs = torch::tensor(allObs).reshape({(int64_t)players.size(), obsSize});
		auto tActionMasks = torch::tensor(allActionMasks).reshape({(int64_t)players.size(), this->actionParser->GetActionAmount()});

		tObs = tObs.to(device);
		tActionMasks = tActionMasks.to(device);
		torch::Tensor tActions, tLogProbs;

		PPOLearner::InferActionsFromModels(*models, tObs, tActionMasks, deterministic, temperature, false, &tActions, &tLogProbs);

		auto actionIndices = TENSOR_TO_VEC<int>(tActions);

		for (int i = 0; i < batchSize; i++)
			results.push_back(actionParser->ParseAction(actionIndices[i], players[i], states[i]));

	} catch (std::exception& e) {
		RG_ERR_CLOSE("InferUnit: Exception when inferring model: " << e.what());
	}

	return results;
}