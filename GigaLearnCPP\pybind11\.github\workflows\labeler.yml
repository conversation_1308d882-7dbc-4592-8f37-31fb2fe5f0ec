name: Labeler
on:
  pull_request_target:
    types: [closed]

permissions: {}

jobs:
  label:
    name: Labeler
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:

    - uses: actions/labeler@main
      if: >
        github.event.pull_request.merged == true &&
        !startsWith(github.event.pull_request.title, 'chore(deps):') &&
        !startsWith(github.event.pull_request.title, 'ci(fix):') &&
        !startsWith(github.event.pull_request.title, 'docs(changelog):')
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        configuration-path: .github/labeler_merged.yml
