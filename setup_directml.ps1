# GigaLearnCPP DirectML Setup Script (PowerShell)
# This script sets up DirectML support for AMD GPUs via vcpkg

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "GigaLearnCPP DirectML Setup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will set up DirectML support for AMD GPUs" -ForegroundColor Green
Write-Host "Optimized for AMD RX 6750 XT + Ryzen 7 5700X" -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "GigaLearnCPP\CMakeLists.txt")) {
    Write-Host "ERROR: Please run this script from the GigaLearnCPP-Leak root directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Checking system requirements..." -ForegroundColor Yellow
Write-Host ""

# Check Windows version
$osVersion = [System.Environment]::OSVersion.Version
Write-Host "Windows version: $($osVersion.Major).$($osVersion.Minor).$($osVersion.Build)"

if ($osVersion.Build -lt 18362) {
    Write-Host "WARNING: Windows 10 version 1903 (build 18362) or later is required for DirectML" -ForegroundColor Yellow
}

# Check for AMD GPU
Write-Host ""
Write-Host "Checking for DirectML-compatible GPUs..." -ForegroundColor Yellow
$gpus = Get-WmiObject -Class Win32_VideoController | Where-Object {$_.Name -like "*AMD*" -or $_.Name -like "*Radeon*" -or $_.Name -like "*Intel*" -or $_.Name -like "*NVIDIA*"}
foreach ($gpu in $gpus) {
    Write-Host "  Found: $($gpu.Name)" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Installing ONNX Runtime with DirectML" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if vcpkg is available
$vcpkgPath = Get-Command vcpkg -ErrorAction SilentlyContinue
if (-not $vcpkgPath) {
    Write-Host "vcpkg not found in PATH. Installing vcpkg..." -ForegroundColor Yellow
    
    # Clone vcpkg if it doesn't exist
    if (-not (Test-Path "vcpkg")) {
        Write-Host "Cloning vcpkg..." -ForegroundColor Yellow
        try {
            git clone https://github.com/Microsoft/vcpkg.git
        } catch {
            Write-Host "ERROR: Failed to clone vcpkg. Please install git first." -ForegroundColor Red
            Write-Host "Download from: https://git-scm.com/download/win" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
    
    # Bootstrap vcpkg
    Write-Host "Bootstrapping vcpkg..." -ForegroundColor Yellow
    Set-Location vcpkg
    try {
        .\bootstrap-vcpkg.bat
    } catch {
        Write-Host "ERROR: Failed to bootstrap vcpkg" -ForegroundColor Red
        Set-Location ..
        Read-Host "Press Enter to exit"
        exit 1
    }
    Set-Location ..
    
    # Add vcpkg to PATH for this session
    $env:PATH += ";$(Get-Location)\vcpkg"
    Write-Host "vcpkg installed successfully!" -ForegroundColor Green
} else {
    Write-Host "vcpkg found in PATH!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Installing ONNX Runtime with DirectML support..." -ForegroundColor Yellow
Write-Host ""

# Install ONNX Runtime via vcpkg
try {
    & vcpkg install onnxruntime[directml]:x64-windows
    Write-Host "ONNX Runtime with DirectML installed successfully via vcpkg!" -ForegroundColor Green
    $installSuccess = $true
} catch {
    Write-Host "WARNING: vcpkg installation failed. Setting up for manual NuGet integration..." -ForegroundColor Yellow
    $installSuccess = $false
}

if (-not $installSuccess) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Alternative: Manual NuGet Integration" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Since vcpkg failed, we'll set up for manual NuGet integration." -ForegroundColor Yellow
    Write-Host "You'll need to use NuGet packages in your CMake configuration." -ForegroundColor Yellow
    Write-Host ""
    
    # Create a simple packages.config for reference
    $packagesConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.ML.OnnxRuntime.DirectML" version="1.22.1" targetFramework="native" />
</packages>
"@
    
    $packagesConfig | Out-File -FilePath "packages.config" -Encoding UTF8
    Write-Host "Created packages.config for NuGet package reference." -ForegroundColor Green
    Write-Host "You can use this with NuGet Package Manager or vcpkg." -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuring CMake for DirectML" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if CMake is available
$cmakePath = Get-Command cmake -ErrorAction SilentlyContinue
if (-not $cmakePath) {
    Write-Host "WARNING: CMake not found in PATH" -ForegroundColor Yellow
    Write-Host "Please install CMake and add it to your PATH" -ForegroundColor Yellow
    Write-Host "Download from: https://cmake.org/download/" -ForegroundColor Yellow
    Write-Host ""
} else {
    $cmakeVersion = & cmake --version | Select-Object -First 1
    Write-Host "CMake found: $cmakeVersion" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Setup Complete!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "DirectML support has been configured for GigaLearnCPP" -ForegroundColor Green
Write-Host ""

if ($installSuccess) {
    Write-Host "Next steps (vcpkg method):" -ForegroundColor Yellow
    Write-Host "  mkdir build && cd build" -ForegroundColor White
    Write-Host "  cmake .. -DENABLE_DIRECTML=ON -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake" -ForegroundColor White
    Write-Host "  cmake --build . --config Release" -ForegroundColor White
} else {
    Write-Host "Next steps (NuGet method):" -ForegroundColor Yellow
    Write-Host "  mkdir build && cd build" -ForegroundColor White
    Write-Host "  cmake .. -DENABLE_DIRECTML=ON -DUSE_NUGET_PACKAGES=ON" -ForegroundColor White
    Write-Host "  cmake --build . --config Release" -ForegroundColor White
}

Write-Host ""
Write-Host "In your code, use:" -ForegroundColor Yellow
Write-Host "  config.deviceType = LearnerDeviceType::GPU_DIRECTML;" -ForegroundColor White
Write-Host "  // or" -ForegroundColor Gray
Write-Host "  config.deviceType = LearnerDeviceType::AUTO;" -ForegroundColor White
Write-Host ""
Write-Host "For your AMD RX 6750 XT, DirectML should provide excellent performance!" -ForegroundColor Green
Write-Host ""

Write-Host "What was installed:" -ForegroundColor Yellow
if (Test-Path "vcpkg") {
    Write-Host "  - vcpkg package manager" -ForegroundColor Green
    if ($installSuccess) {
        Write-Host "  - ONNX Runtime with DirectML via vcpkg" -ForegroundColor Green
    }
} 
if (-not $installSuccess) {
    Write-Host "  - packages.config for NuGet integration" -ForegroundColor Green
}

Write-Host ""
Write-Host "If you encounter issues:" -ForegroundColor Yellow
Write-Host "  - Make sure your GPU drivers are up to date (AMD Adrenalin)" -ForegroundColor White
Write-Host "  - Ensure Windows 10 version 1903 or later" -ForegroundColor White
Write-Host "  - Check that your GPU supports DirectML" -ForegroundColor White
Write-Host "  - Run tools/test_directml.exe after building to verify" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
