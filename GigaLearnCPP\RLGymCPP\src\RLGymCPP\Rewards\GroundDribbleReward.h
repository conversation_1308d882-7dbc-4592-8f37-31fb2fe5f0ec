#pragma once
#include "Reward.h"

namespace RLGC {
	// C++ version of your GroundDribbleReward from customreward.py
	// Rewards the bot for maintaining a ground dribble.
	class GroundDribbleReward : public Reward {
	public:
		constexpr static float DISTANCE_THRESHOLD = 162.5f;
		constexpr static float BALL_HEIGHT_LOWER_THRESHOLD = 110.0f;
        constexpr static float BALL_HEIGHT_UPPER_THRESHOLD = 180.0f; // Added for more stability

		virtual float GetReward(const Player& player, const GameState& state, bool isFinal) override {
			// Player must be on the ground
			if (!player.isOnGround) {
				return 0;
			}

			// Ball must be within a reasonable height range for dribbling
			if (state.ball.pos.z < BALL_HEIGHT_LOWER_THRESHOLD || state.ball.pos.z > BALL_HEIGHT_UPPER_THRESHOLD) {
				return 0;
			}

			// Player must be close to the ball
			float distToBall = (state.ball.pos - player.pos).Length();
			if (distToBall > DISTANCE_THRESHOLD) {
				return 0;
			}

			// If all conditions are met, give the reward
			return 1.0f;
		}
	};
}