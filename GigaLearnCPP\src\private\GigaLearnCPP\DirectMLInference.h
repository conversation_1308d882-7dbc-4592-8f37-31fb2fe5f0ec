#pragma once

#ifdef RG_DIRECTML_SUPPORT

#include <GigaLearnCPP/Framework.h>
#include <vector>
#include <memory>
#include <string>

// Forward declarations to avoid including ONNX Runtime headers in public headers
namespace Ort {
    class Session;
    class Env;
    class SessionOptions;
    class MemoryInfo;
    class Value;
}

namespace GGL {
    
    /**
     * DirectML inference wrapper for ONNX Runtime
     * Provides GPU acceleration for AMD GPUs via DirectML
     */
    class DirectMLInference {
    public:
        DirectMLInference();
        ~DirectMLInference();
        
        // Initialize DirectML with ONNX Runtime
        bool Initialize();
        
        // Check if DirectML is available
        static bool IsDirectMLAvailable();
        
        // Load a model from ONNX file
        bool LoadModel(const std::string& modelPath);
        
        // Run inference on input tensors
        std::vector<std::vector<float>> RunInference(
            const std::vector<std::vector<float>>& inputs,
            const std::vector<std::vector<int64_t>>& inputShapes
        );
        
        // Convert PyTorch tensor to ONNX format and run inference
        std::vector<std::vector<float>> RunInferenceFromTorch(
            const std::vector<torch::Tensor>& torchInputs
        );
        
        // Get model input/output information
        std::vector<std::string> GetInputNames() const;
        std::vector<std::string> GetOutputNames() const;
        std::vector<std::vector<int64_t>> GetInputShapes() const;
        std::vector<std::vector<int64_t>> GetOutputShapes() const;
        
        // Cleanup
        void Cleanup();
        
    private:
        std::unique_ptr<Ort::Env> env_;
        std::unique_ptr<Ort::Session> session_;
        std::unique_ptr<Ort::SessionOptions> sessionOptions_;
        std::unique_ptr<Ort::MemoryInfo> memoryInfo_;
        
        std::vector<std::string> inputNames_;
        std::vector<std::string> outputNames_;
        std::vector<std::vector<int64_t>> inputShapes_;
        std::vector<std::vector<int64_t>> outputShapes_;
        
        bool initialized_;
        bool modelLoaded_;
        
        // Helper methods
        std::vector<Ort::Value> CreateInputTensors(
            const std::vector<std::vector<float>>& inputs,
            const std::vector<std::vector<int64_t>>& shapes
        );
        
        std::vector<std::vector<float>> ExtractOutputTensors(
            std::vector<Ort::Value>& outputs
        );
        
        // Convert torch tensor to flat vector
        std::vector<float> TorchTensorToVector(const torch::Tensor& tensor);
        std::vector<int64_t> TorchShapeToVector(const torch::Tensor& tensor);
    };
    
    /**
     * DirectML Model Manager
     * Handles model conversion and caching for DirectML inference
     */
    class DirectMLModelManager {
    public:
        DirectMLModelManager();
        ~DirectMLModelManager();
        
        // Convert PyTorch model to ONNX format for DirectML
        bool ConvertTorchModelToONNX(
            torch::nn::Module& model,
            const std::vector<torch::Tensor>& exampleInputs,
            const std::string& outputPath
        );
        
        // Get or create DirectML inference instance for a model
        std::shared_ptr<DirectMLInference> GetInference(const std::string& modelKey);
        
        // Register a model for DirectML inference
        bool RegisterModel(
            const std::string& modelKey,
            const std::string& onnxPath
        );
        
        // Check if a model is available for DirectML inference
        bool IsModelAvailable(const std::string& modelKey) const;
        
        // Clear all cached models
        void ClearCache();
        
    private:
        std::map<std::string, std::shared_ptr<DirectMLInference>> inferenceCache_;
        std::map<std::string, std::string> modelPaths_;
        
        // Generate unique model key from torch module
        std::string GenerateModelKey(const torch::nn::Module& model);
    };
    
    // Global DirectML manager instance
    extern DirectMLModelManager g_directMLManager;
}

#endif // RG_DIRECTML_SUPPORT
