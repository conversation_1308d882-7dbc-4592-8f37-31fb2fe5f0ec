#pragma once
#include "Reward.h"
#include "../Math.h" // For RS_MAX

namespace RLGC {
	// C++ version of your SwiftGroundDribbleReward from customreward.py
	// Rewards the bot for maintaining a high-speed ground dribble.
	class SwiftGroundDribbleReward : public Reward {
	public:
		constexpr static float MIN_BALL_HEIGHT = 109.0f;
		constexpr static float MAX_BALL_HEIGHT = 180.0f;
		constexpr static float MAX_DISTANCE = 197.0f;
		constexpr static float COEFF = 2.0f;

		virtual float GetReward(const Player& player, const GameState& state, bool isFinal) override {
			// Player must be on the ground and ball must be at a good height and distance
			if (!player.isOnGround ||
				state.ball.pos.z < MIN_BALL_HEIGHT ||
				state.ball.pos.z > MAX_BALL_HEIGHT ||
				(player.pos - state.ball.pos).Length() >= MAX_DISTANCE) {
				return 0.0f;
			}

			float playerSpeed = player.vel.Length();
			float ballSpeed = state.ball.vel.Length();

			float playerSpeedNormalized = playerSpeed / CommonValues::CAR_MAX_SPEED;
			float inverseDifference = 1.0f - abs(playerSpeed - ballSpeed);
			float twoSum = playerSpeed + ballSpeed;

			// Prevent division by zero if both are somehow perfectly still
			if (twoSum < 1e-6) {
				return 0.0f;
			}

			float speedReward = playerSpeedNormalized + COEFF * (inverseDifference / twoSum);
			
			return speedReward;
		}
	};
}