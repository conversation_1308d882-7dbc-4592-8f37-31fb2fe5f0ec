#include "DirectMLInference.h"

#ifdef RG_DIRECTML_SUPPORT

#include <onnxruntime_cxx_api.h>
#include <iostream>
#include <filesystem>

namespace GGL {

// Global DirectML manager instance
DirectMLModelManager g_directMLManager;

DirectMLInference::DirectMLInference() 
    : initialized_(false), modelLoaded_(false) {
}

DirectMLInference::~DirectMLInference() {
    Cleanup();
}

bool DirectMLInference::Initialize() {
    try {
        // Create ONNX Runtime environment
        env_ = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "GigaLearnDirectML");
        
        // Create session options with DirectML provider
        sessionOptions_ = std::make_unique<Ort::SessionOptions>();
        
        // Enable DirectML execution provider
        // This will use the default DirectML device (usually the most powerful GPU)
        OrtSessionOptionsAppendExecutionProvider_DML(*sessionOptions_, 0);
        
        // Set optimization level
        sessionOptions_->SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);
        
        // Create memory info for CPU (input/output tensors)
        memoryInfo_ = std::make_unique<Ort::MemoryInfo>(
            Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault)
        );
        
        initialized_ = true;
        RG_LOG("DirectML inference initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        RG_LOG("Failed to initialize DirectML: " << e.what());
        return false;
    }
}

bool DirectMLInference::IsDirectMLAvailable() {
    try {
        // Try to create a simple session with DirectML provider
        Ort::Env testEnv(ORT_LOGGING_LEVEL_ERROR, "DirectMLTest");
        Ort::SessionOptions testOptions;
        OrtSessionOptionsAppendExecutionProvider_DML(testOptions, 0);
        
        // If we get here without exception, DirectML is available
        return true;
        
    } catch (...) {
        return false;
    }
}

bool DirectMLInference::LoadModel(const std::string& modelPath) {
    if (!initialized_) {
        RG_LOG("DirectML not initialized");
        return false;
    }
    
    if (!std::filesystem::exists(modelPath)) {
        RG_LOG("ONNX model file not found: " << modelPath);
        return false;
    }
    
    try {
        // Create session from ONNX model
        session_ = std::make_unique<Ort::Session>(*env_, modelPath.c_str(), *sessionOptions_);
        
        // Get input information
        size_t numInputs = session_->GetInputCount();
        inputNames_.clear();
        inputShapes_.clear();
        
        for (size_t i = 0; i < numInputs; i++) {
            // Get input name
            char* inputName = session_->GetInputName(i, Ort::AllocatorWithDefaultOptions());
            inputNames_.push_back(std::string(inputName));
            
            // Get input shape
            Ort::TypeInfo inputTypeInfo = session_->GetInputTypeInfo(i);
            auto inputTensorInfo = inputTypeInfo.GetTensorTypeAndShapeInfo();
            std::vector<int64_t> inputShape = inputTensorInfo.GetShape();
            inputShapes_.push_back(inputShape);
        }
        
        // Get output information
        size_t numOutputs = session_->GetOutputCount();
        outputNames_.clear();
        outputShapes_.clear();
        
        for (size_t i = 0; i < numOutputs; i++) {
            // Get output name
            char* outputName = session_->GetOutputName(i, Ort::AllocatorWithDefaultOptions());
            outputNames_.push_back(std::string(outputName));
            
            // Get output shape
            Ort::TypeInfo outputTypeInfo = session_->GetOutputTypeInfo(i);
            auto outputTensorInfo = outputTypeInfo.GetTensorTypeAndShapeInfo();
            std::vector<int64_t> outputShape = outputTensorInfo.GetShape();
            outputShapes_.push_back(outputShape);
        }
        
        modelLoaded_ = true;
        RG_LOG("DirectML model loaded: " << modelPath);
        RG_LOG("  Inputs: " << numInputs << ", Outputs: " << numOutputs);
        
        return true;
        
    } catch (const std::exception& e) {
        RG_LOG("Failed to load DirectML model: " << e.what());
        return false;
    }
}

std::vector<std::vector<float>> DirectMLInference::RunInference(
    const std::vector<std::vector<float>>& inputs,
    const std::vector<std::vector<int64_t>>& inputShapes) {
    
    if (!modelLoaded_) {
        RG_LOG("No model loaded for DirectML inference");
        return {};
    }
    
    try {
        // Create input tensors
        auto inputTensors = CreateInputTensors(inputs, inputShapes);
        
        // Prepare input/output names as char*
        std::vector<const char*> inputNamesCStr;
        std::vector<const char*> outputNamesCStr;
        
        for (const auto& name : inputNames_) {
            inputNamesCStr.push_back(name.c_str());
        }
        for (const auto& name : outputNames_) {
            outputNamesCStr.push_back(name.c_str());
        }
        
        // Run inference
        auto outputTensors = session_->Run(
            Ort::RunOptions{nullptr},
            inputNamesCStr.data(),
            inputTensors.data(),
            inputTensors.size(),
            outputNamesCStr.data(),
            outputNames_.size()
        );
        
        // Extract results
        return ExtractOutputTensors(outputTensors);
        
    } catch (const std::exception& e) {
        RG_LOG("DirectML inference failed: " << e.what());
        return {};
    }
}

std::vector<std::vector<float>> DirectMLInference::RunInferenceFromTorch(
    const std::vector<torch::Tensor>& torchInputs) {
    
    std::vector<std::vector<float>> inputs;
    std::vector<std::vector<int64_t>> shapes;
    
    for (const auto& tensor : torchInputs) {
        inputs.push_back(TorchTensorToVector(tensor));
        shapes.push_back(TorchShapeToVector(tensor));
    }
    
    return RunInference(inputs, shapes);
}

std::vector<std::string> DirectMLInference::GetInputNames() const {
    return inputNames_;
}

std::vector<std::string> DirectMLInference::GetOutputNames() const {
    return outputNames_;
}

std::vector<std::vector<int64_t>> DirectMLInference::GetInputShapes() const {
    return inputShapes_;
}

std::vector<std::vector<int64_t>> DirectMLInference::GetOutputShapes() const {
    return outputShapes_;
}

void DirectMLInference::Cleanup() {
    session_.reset();
    sessionOptions_.reset();
    memoryInfo_.reset();
    env_.reset();
    
    inputNames_.clear();
    outputNames_.clear();
    inputShapes_.clear();
    outputShapes_.clear();
    
    initialized_ = false;
    modelLoaded_ = false;
}

std::vector<Ort::Value> DirectMLInference::CreateInputTensors(
    const std::vector<std::vector<float>>& inputs,
    const std::vector<std::vector<int64_t>>& shapes) {
    
    std::vector<Ort::Value> inputTensors;
    
    for (size_t i = 0; i < inputs.size(); i++) {
        // Calculate total size
        int64_t totalSize = 1;
        for (int64_t dim : shapes[i]) {
            totalSize *= dim;
        }
        
        // Create tensor
        auto tensor = Ort::Value::CreateTensor<float>(
            *memoryInfo_,
            const_cast<float*>(inputs[i].data()),
            totalSize,
            shapes[i].data(),
            shapes[i].size()
        );
        
        inputTensors.push_back(std::move(tensor));
    }
    
    return inputTensors;
}

std::vector<std::vector<float>> DirectMLInference::ExtractOutputTensors(
    std::vector<Ort::Value>& outputs) {
    
    std::vector<std::vector<float>> results;
    
    for (auto& output : outputs) {
        // Get tensor data
        float* outputData = output.GetTensorMutableData<float>();
        
        // Get tensor shape info
        auto tensorInfo = output.GetTensorTypeAndShapeInfo();
        auto shape = tensorInfo.GetShape();
        
        // Calculate total size
        int64_t totalSize = 1;
        for (int64_t dim : shape) {
            totalSize *= dim;
        }
        
        // Copy data to vector
        std::vector<float> outputVector(outputData, outputData + totalSize);
        results.push_back(std::move(outputVector));
    }
    
    return results;
}

std::vector<float> DirectMLInference::TorchTensorToVector(const torch::Tensor& tensor) {
    // Ensure tensor is contiguous and on CPU
    auto cpuTensor = tensor.contiguous().cpu();
    
    // Get data pointer and size
    float* data = cpuTensor.data_ptr<float>();
    int64_t size = cpuTensor.numel();
    
    return std::vector<float>(data, data + size);
}

std::vector<int64_t> DirectMLInference::TorchShapeToVector(const torch::Tensor& tensor) {
    auto sizes = tensor.sizes();
    return std::vector<int64_t>(sizes.begin(), sizes.end());
}

// DirectMLModelManager implementation
DirectMLModelManager::DirectMLModelManager() {
}

DirectMLModelManager::~DirectMLModelManager() {
    ClearCache();
}

bool DirectMLModelManager::ConvertTorchModelToONNX(
    torch::nn::Module& model,
    const std::vector<torch::Tensor>& exampleInputs,
    const std::string& outputPath) {

    try {
        // Set model to evaluation mode
        model.eval();

        // Create the directory if it doesn't exist
        std::filesystem::path outputDir = std::filesystem::path(outputPath).parent_path();
        if (!outputDir.empty()) {
            std::filesystem::create_directories(outputDir);
        }

        // Export to ONNX using torch::jit::trace
        torch::jit::script::Module tracedModel = torch::jit::trace(model, exampleInputs);
        tracedModel.save(outputPath);

        RG_LOG("Model exported to ONNX: " << outputPath);
        return true;

    } catch (const std::exception& e) {
        RG_LOG("Failed to convert model to ONNX: " << e.what());
        return false;
    }
}

std::shared_ptr<DirectMLInference> DirectMLModelManager::GetInference(const std::string& modelKey) {
    auto it = inferenceCache_.find(modelKey);
    if (it != inferenceCache_.end()) {
        return it->second;
    }

    // Check if we have a path for this model
    auto pathIt = modelPaths_.find(modelKey);
    if (pathIt == modelPaths_.end()) {
        return nullptr;
    }

    // Create new inference instance
    auto inference = std::make_shared<DirectMLInference>();
    if (!inference->Initialize()) {
        return nullptr;
    }

    if (!inference->LoadModel(pathIt->second)) {
        return nullptr;
    }

    // Cache the inference instance
    inferenceCache_[modelKey] = inference;
    return inference;
}

bool DirectMLModelManager::RegisterModel(
    const std::string& modelKey,
    const std::string& onnxPath) {

    if (!std::filesystem::exists(onnxPath)) {
        RG_LOG("ONNX model file not found: " << onnxPath);
        return false;
    }

    modelPaths_[modelKey] = onnxPath;

    // Remove from cache if it exists (force reload)
    auto it = inferenceCache_.find(modelKey);
    if (it != inferenceCache_.end()) {
        inferenceCache_.erase(it);
    }

    return true;
}

bool DirectMLModelManager::IsModelAvailable(const std::string& modelKey) const {
    return modelPaths_.find(modelKey) != modelPaths_.end();
}

void DirectMLModelManager::ClearCache() {
    inferenceCache_.clear();
    modelPaths_.clear();
}

std::string DirectMLModelManager::GenerateModelKey(const torch::nn::Module& model) {
    // Generate a simple key based on model parameters
    // In a real implementation, you might want to use a hash of the model structure
    std::string key = "model_" + std::to_string(reinterpret_cast<uintptr_t>(&model));
    return key;
}

} // namespace GGL

#endif // RG_DIRECTML_SUPPORT
