#pragma once
#include "Reward.h"

namespace RLGC {
	class BehindBallReward : public Reward {
	public:
		virtual float GetReward(const Player& player, const GameState& state, bool isFinal) {
			Vec ownGoalPos = (player.team == Team::BLUE) ? CommonValues::BLUE_GOAL_BACK : CommonValues::ORANGE_GOAL_BACK;

			Vec playerToBall = (state.ball.pos - player.pos).Normalized();
			Vec playerToGoal = (ownGoalPos - player.pos).Normalized();

			// Calculate the dot product. It's > 0 if the ball and goal are in the same general direction from the player.
			// This means the player is NOT between the ball and the goal.
			// We want the dot product to be < 0, so we return the negative of it.
			float dot = playerToBall.Dot(playerToGoal);
			
			// A positive reward for being between ball and goal, max of 1.
			return -RS_CLAMP(dot, -1, 0);
		}
	};
}