#pragma once
#include "ObsBuilder.h"
#include <RLGymCPP/Gamestates/StateUtil.h>
#include "AdvancedObs.h" // <-- THIS IS THE FIX. It makes the file aware of the AdvancedObs class.

namespace RLGC {
	// A corrected version of AdvancedObsPadder that matches your GigaLearn API
	class AdvancedObsPadder : public ObsBuilder {
	public:
		int teamSize;

		AdvancedObsPadder(int teamSize = 1) : teamSize(teamSize) {}

		// Corrected function signature to match the ObsBuilder base class
		virtual FList BuildObs(const Player& player, const GameState& state) override {
			FList obs = {};

			bool inv = (player.team == Team::ORANGE);

			auto ball = InvertPhys(state.ball, inv);
			auto& pads = state.GetBoostPads(inv);
			auto& padTimers = state.GetBoostPadTimers(inv);

			obs += ball.pos * AdvancedObs::POS_COEF;
			obs += ball.vel * AdvancedObs::VEL_COEF;
			obs += ball.angVel * AdvancedObs::ANG_VEL_COEF;

			for (int i = 0; i < player.prevAction.ELEM_AMOUNT; i++)
				obs += player.prevAction[i];
			
			for (int i = 0; i < CommonValues::BOOST_LOCATIONS_AMOUNT; i++) {
				if (pads[i]) {
					obs += 1.f;
				} else {
					obs += 1.f / (1.f + padTimers[i]);
				}
			}

			// Player Data
			FList teammates = {}, opponents = {};

			for (auto& otherPlayer : state.players) {
				if (otherPlayer.carId == player.carId) continue;
				if (otherPlayer.team == player.team) {
					AddPlayerToObs(teammates, otherPlayer, inv, ball);
				} else {
					AddPlayerToObs(opponents, otherPlayer, inv, ball);
				}
			}

			// Add self and pad others
			AddPlayerToObs(obs, player, inv, ball);

			// Pad and add teammates
			int teammateCount = teammates.size() / 28; // 28 floats per player in AdvancedObs
			obs += teammates;
			for (int i = 0; i < (teamSize - 1) - teammateCount; i++)
				_addDummyPlayer(obs);

			// Pad and add opponents
			int opponentCount = opponents.size() / 28;
			obs += opponents;
			for (int i = 0; i < teamSize - opponentCount; i++)
				_addDummyPlayer(obs);
			
			return obs;
		}

	private:
		// Re-using the AddPlayerToObs function from the base AdvancedObs
		void AddPlayerToObs(FList& obs, const Player& player, bool inv, const PhysState& ball) {
			auto phys = InvertPhys(player, inv);

			obs += phys.pos * AdvancedObs::POS_COEF;
			obs += phys.rotMat.forward;
			obs += phys.rotMat.up;
			obs += phys.vel * AdvancedObs::VEL_COEF;
			obs += phys.angVel * AdvancedObs::ANG_VEL_COEF;
			obs += phys.rotMat.Dot(phys.angVel) * AdvancedObs::ANG_VEL_COEF;

			obs += phys.rotMat.Dot(ball.pos - phys.pos) * AdvancedObs::POS_COEF;
			obs += phys.rotMat.Dot(ball.vel - phys.vel) * AdvancedObs::VEL_COEF;

			obs += player.boost / 100;
			obs += player.isOnGround;
			obs += player.HasFlipOrJump();
			obs += player.isDemoed;
			obs += player.hasJumped;
		}

		void _addDummyPlayer(FList& obs) {
			// A single player's observation is 28 floats in this implementation
			for (int i = 0; i < 28; i++) {
				obs += 0.0f;
			}
		}
	};
}