#pragma once
#include "Reward.h"

namespace RLGC {
    // Rewards the bot for staying near the ball after touching it
	class PossessionReward : public Reward {
    private:
        // We will store the index of the player who last touched the ball
        int _lastTouchPlayerIndex = -1;

    public:
        // The distance at which the reward becomes zero
        float maxDist;

        PossessionReward(float maxDist = 1000.f) : maxDist(maxDist) {}

        // We use PreStep to check who touched the ball in the current step
        virtual void PreStep(const GameState& state) override {
            for (const auto& player : state.players) {
                if (player.ballTouchedStep) {
                    _lastTouchPlayerIndex = player.index;
                    break; // Assume only one player can touch the ball per step
                }
            }
        }

		virtual float GetReward(const Player& player, const GameState& state, bool isFinal) override {
			if (_lastTouchPlayerIndex != player.index) {
                // We didn't touch the ball last, so no reward
				return 0;
            }

            float dist = (player.pos - state.ball.pos).Length();

            if (dist >= maxDist) {
                return 0;
            }

            // Reward is 1 when on the ball, and decays linearly to 0 at maxDist
            return 1 - (dist / maxDist);
		}
	};
}